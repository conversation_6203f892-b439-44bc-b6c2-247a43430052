/**
 * 浏览器指纹收集器 - 仅用于学习和研究目的
 * Browser Fingerprint Collector - For Educational Purposes Only
 * 
 * 警告：此代码仅用于学习浏览器指纹技术原理，不应用于恶意目的
 * Warning: This code is for educational purposes only to understand browser fingerprinting
 */

class BrowserFingerprintCollector {
    constructor() {
        this.fingerprint = {};
        this.canvas = null;
        this.audioContext = null;
        this.networkRequests = [];
        this.apiEndpoints = [];
        this.videoSources = [];
        this.setupNetworkInterception();
    }

    /**
     * 设置网络请求拦截 - 用于学术研究API分析
     */
    setupNetworkInterception() {
        const self = this;

        // 拦截 Fetch API
        const originalFetch = window.fetch;
        window.fetch = function (...args) {
            const url = typeof args[0] === 'string' ? args[0] : args[0].url;
            const options = args[1] || {};

            self.logNetworkRequest('fetch', url, options);

            return originalFetch.apply(this, args).then(response => {
                self.analyzeResponse('fetch', url, response.clone());
                return response;
            }).catch(error => {
                console.log('Fetch error:', error);
                return Promise.reject(error);
            });
        };

        // 拦截 XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;

        XMLHttpRequest.prototype.open = function (method, url, ...args) {
            this._method = method;
            this._url = url;
            return originalXHROpen.apply(this, [method, url, ...args]);
        };

        XMLHttpRequest.prototype.send = function (data) {
            const xhr = this;
            self.logNetworkRequest('xhr', this._url, { method: this._method, data });

            const originalOnReadyStateChange = this.onreadystatechange;
            this.onreadystatechange = function () {
                if (this.readyState === 4) {
                    self.analyzeResponse('xhr', xhr._url, {
                        status: xhr.status,
                        responseText: xhr.responseText,
                        responseHeaders: xhr.getAllResponseHeaders()
                    });
                }
                if (originalOnReadyStateChange) {
                    originalOnReadyStateChange.apply(this, arguments);
                }
            };

            return originalXHRSend.apply(this, arguments);
        };
    }

    /**
     * 记录网络请求 - 学术研究用
     */
    logNetworkRequest(type, url, options = {}) {
        if (!url) return;

        const request = {
            type,
            url,
            method: options.method || 'GET',
            timestamp: Date.now(),
            headers: options.headers || {},
            data: options.data || null
        };

        this.networkRequests.push(request);

        // 检测视频相关API
        if (this.isVideoRelatedURL(url)) {
            this.apiEndpoints.push({
                ...request,
                category: 'video_api'
            });
            console.log('🎥 检测到视频API:', url);
        }

        // 检测媒体文件
        if (this.isMediaURL(url)) {
            this.videoSources.push({
                ...request,
                category: 'media_file'
            });
            console.log('📹 检测到媒体文件:', url);
        }
    }

    /**
     * 分析响应数据 - 学术研究用
     */
    async analyzeResponse(type, url, response) {
        try {
            let responseData = null;

            if (type === 'fetch' && response.clone) {
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    responseData = await response.json();
                } else {
                    responseData = await response.text();
                }
            } else if (type === 'xhr') {
                responseData = response.responseText;
                try {
                    responseData = JSON.parse(responseData);
                } catch (e) {
                    // 保持原始文本
                }
            }

            // 分析视频相关响应
            if (this.isVideoRelatedURL(url) && responseData) {
                this.analyzeVideoAPIResponse(url, responseData);
            }

        } catch (error) {
            console.log('Response analysis error:', error);
        }
    }

    /**
     * 判断是否为视频相关URL
     */
    isVideoRelatedURL(url) {
        const videoKeywords = [
            'video', 'media', 'stream', 'play', 'episode', 'series',
            'm3u8', 'mp4', 'webm', 'avi', 'mov', 'flv',
            'api', 'cdn', 'content', 'asset'
        ];

        return videoKeywords.some(keyword =>
            url.toLowerCase().includes(keyword)
        );
    }

    /**
     * 判断是否为媒体文件URL
     */
    isMediaURL(url) {
        const mediaExtensions = [
            '.mp4', '.webm', '.avi', '.mov', '.flv', '.mkv',
            '.m3u8', '.mpd', '.ts', '.m4s'
        ];

        return mediaExtensions.some(ext =>
            url.toLowerCase().includes(ext)
        );
    }

    /**
     * 分析视频API响应
     */
    analyzeVideoAPIResponse(url, data) {
        console.log('🔍 分析视频API响应:', url);

        // 查找可能的视频URL
        const videoUrls = this.extractVideoURLsFromData(data);
        if (videoUrls.length > 0) {
            console.log('📹 发现视频URL:', videoUrls);
            this.videoSources.push(...videoUrls.map(videoUrl => ({
                type: 'extracted',
                url: videoUrl,
                source: url,
                timestamp: Date.now(),
                category: 'extracted_video'
            })));
        }

        // 查找剧集信息
        const episodes = this.extractEpisodeInfo(data);
        if (episodes.length > 0) {
            console.log('📺 发现剧集信息:', episodes);
        }
    }

    /**
     * 从数据中提取视频URL
     */
    extractVideoURLsFromData(data) {
        const urls = [];
        const dataStr = JSON.stringify(data);

        // 正则表达式匹配视频URL
        const urlPatterns = [
            /https?:\/\/[^\s"'<>]+\.(?:mp4|webm|avi|mov|flv|mkv|m3u8|mpd)/gi,
            /https?:\/\/[^\s"'<>]*(?:video|media|stream|cdn)[^\s"'<>]*/gi
        ];

        urlPatterns.forEach(pattern => {
            const matches = dataStr.match(pattern);
            if (matches) {
                urls.push(...matches);
            }
        });

        return [...new Set(urls)]; // 去重
    }

    /**
     * 提取剧集信息
     */
    extractEpisodeInfo(data) {
        const episodes = [];

        if (Array.isArray(data)) {
            data.forEach(item => {
                if (item && (item.title || item.name || item.episode)) {
                    episodes.push({
                        title: item.title || item.name,
                        episode: item.episode || item.number,
                        url: item.url || item.link,
                        thumbnail: item.thumbnail || item.image
                    });
                }
            });
        } else if (data && typeof data === 'object') {
            // 递归搜索对象
            Object.values(data).forEach(value => {
                if (Array.isArray(value)) {
                    episodes.push(...this.extractEpisodeInfo(value));
                }
            });
        }

        return episodes;
    }

    /**
     * 收集基础浏览器信息
     */
    collectBasicInfo() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            languages: navigator.languages,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            doNotTrack: navigator.doNotTrack,
            hardwareConcurrency: navigator.hardwareConcurrency,
            maxTouchPoints: navigator.maxTouchPoints,
            vendor: navigator.vendor,
            vendorSub: navigator.vendorSub,
            productSub: navigator.productSub,
            buildID: navigator.buildID || 'N/A'
        };
    }

    /**
     * 收集屏幕信息
     */
    collectScreenInfo() {
        return {
            screenWidth: screen.width,
            screenHeight: screen.height,
            availWidth: screen.availWidth,
            availHeight: screen.availHeight,
            colorDepth: screen.colorDepth,
            pixelDepth: screen.pixelDepth,
            devicePixelRatio: window.devicePixelRatio,
            orientation: screen.orientation ? screen.orientation.type : 'N/A'
        };
    }

    /**
     * 收集时区信息
     */
    collectTimezoneInfo() {
        const date = new Date();
        return {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezoneOffset: date.getTimezoneOffset(),
            locale: Intl.DateTimeFormat().resolvedOptions().locale,
            dateString: date.toString(),
            utcString: date.toUTCString()
        };
    }

    /**
     * 收集插件信息
     */
    collectPluginsInfo() {
        const plugins = [];
        for (let i = 0; i < navigator.plugins.length; i++) {
            const plugin = navigator.plugins[i];
            plugins.push({
                name: plugin.name,
                description: plugin.description,
                filename: plugin.filename,
                version: plugin.version || 'N/A'
            });
        }
        return plugins;
    }

    /**
     * 收集字体信息
     */
    async collectFontsInfo() {
        const testFonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact', 'SimSun', 'Microsoft YaHei',
            'PingFang SC', 'Hiragino Sans GB', 'Source Han Sans CN'
        ];

        const availableFonts = [];
        const testString = 'mmmmmmmmmmlli';
        const testSize = '72px';
        const baseFonts = ['monospace', 'sans-serif', 'serif'];

        // 创建测试元素
        const span = document.createElement('span');
        span.style.fontSize = testSize;
        span.style.position = 'absolute';
        span.style.left = '-9999px';
        span.innerHTML = testString;
        document.body.appendChild(span);

        // 获取基础字体的尺寸
        const baseSizes = {};
        baseFonts.forEach(baseFont => {
            span.style.fontFamily = baseFont;
            baseSizes[baseFont] = {
                width: span.offsetWidth,
                height: span.offsetHeight
            };
        });

        // 测试每个字体
        testFonts.forEach(font => {
            let detected = false;
            baseFonts.forEach(baseFont => {
                span.style.fontFamily = `${font}, ${baseFont}`;
                const size = {
                    width: span.offsetWidth,
                    height: span.offsetHeight
                };

                if (size.width !== baseSizes[baseFont].width ||
                    size.height !== baseSizes[baseFont].height) {
                    detected = true;
                }
            });

            if (detected) {
                availableFonts.push(font);
            }
        });

        document.body.removeChild(span);
        return availableFonts;
    }

    /**
     * 生成Canvas指纹
     */
    generateCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            canvas.width = 200;
            canvas.height = 50;

            // 绘制文本
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            ctx.fillStyle = '#069';
            ctx.fillText('Hello, World! 🌍', 2, 15);
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Canvas Fingerprint', 4, 35);

            // 绘制几何图形
            ctx.globalCompositeOperation = 'multiply';
            ctx.fillStyle = 'rgb(255,0,255)';
            ctx.beginPath();
            ctx.arc(50, 50, 50, 0, Math.PI * 2, true);
            ctx.closePath();
            ctx.fill();

            return canvas.toDataURL();
        } catch (e) {
            return 'Canvas not supported';
        }
    }

    /**
     * 生成WebGL指纹
     */
    generateWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

            if (!gl) {
                return 'WebGL not supported';
            }

            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');

            return {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER),
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                unmaskedVendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'N/A',
                unmaskedRenderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'N/A',
                maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS),
                extensions: gl.getSupportedExtensions()
            };
        } catch (e) {
            return 'WebGL error: ' + e.message;
        }
    }

    /**
     * 生成音频指纹
     */
    generateAudioFingerprint() {
        return new Promise((resolve) => {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const analyser = audioContext.createAnalyser();
                const gainNode = audioContext.createGain();
                const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);

                oscillator.type = 'triangle';
                oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);

                gainNode.gain.setValueAtTime(0, audioContext.currentTime);

                oscillator.connect(analyser);
                analyser.connect(scriptProcessor);
                scriptProcessor.connect(gainNode);
                gainNode.connect(audioContext.destination);

                scriptProcessor.onaudioprocess = function (bins) {
                    const array = new Float32Array(analyser.frequencyBinCount);
                    analyser.getFloatFrequencyData(array);

                    let fingerprint = 0;
                    for (let i = 0; i < array.length; i++) {
                        fingerprint += array[i];
                    }

                    oscillator.disconnect();
                    scriptProcessor.disconnect();
                    audioContext.close();

                    resolve(fingerprint.toString());
                };

                oscillator.start(0);
            } catch (e) {
                resolve('Audio fingerprint not supported: ' + e.message);
            }
        });
    }

    /**
     * 检测存储信息
     */
    collectStorageInfo() {
        const storage = {};

        // LocalStorage
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            storage.localStorage = true;
        } catch (e) {
            storage.localStorage = false;
        }

        // SessionStorage
        try {
            sessionStorage.setItem('test', 'test');
            sessionStorage.removeChild('test');
            storage.sessionStorage = true;
        } catch (e) {
            storage.sessionStorage = false;
        }

        // IndexedDB
        storage.indexedDB = !!window.indexedDB;

        // WebSQL
        storage.webSQL = !!window.openDatabase;

        return storage;
    }

    /**
     * 生成唯一指纹哈希
     */
    generateFingerprintHash(data) {
        const str = JSON.stringify(data);
        let hash = 0;

        if (str.length === 0) return hash.toString();

        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }

        return Math.abs(hash).toString(16);
    }

    /**
     * 收集页面中的视频元素 - 学术研究用
     */
    collectVideoElements() {
        const videoElements = [];

        // 收集video标签
        const videos = document.querySelectorAll('video');
        videos.forEach((video, index) => {
            const videoData = {
                index,
                tagName: 'video',
                src: video.src || video.currentSrc,
                poster: video.poster,
                duration: video.duration,
                videoWidth: video.videoWidth,
                videoHeight: video.videoHeight,
                sources: []
            };

            // 收集source标签
            const sources = video.querySelectorAll('source');
            sources.forEach(source => {
                videoData.sources.push({
                    src: source.src,
                    type: source.type,
                    media: source.media
                });
            });

            videoElements.push(videoData);
        });

        // 收集iframe中的视频
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach((iframe, index) => {
            if (iframe.src && this.isVideoRelatedURL(iframe.src)) {
                videoElements.push({
                    index,
                    tagName: 'iframe',
                    src: iframe.src,
                    width: iframe.width,
                    height: iframe.height
                });
            }
        });

        return videoElements;
    }

    /**
     * 分析页面DOM结构 - 学术研究用
     */
    analyzeDOMStructure() {
        const domAnalysis = {
            totalElements: document.querySelectorAll('*').length,
            scripts: document.querySelectorAll('script').length,
            iframes: document.querySelectorAll('iframe').length,
            videos: document.querySelectorAll('video').length,
            audios: document.querySelectorAll('audio').length,
            forms: document.querySelectorAll('form').length,
            inputs: document.querySelectorAll('input').length,
            links: document.querySelectorAll('a').length,
            images: document.querySelectorAll('img').length
        };

        // 分析可能的视频播放器
        const playerSelectors = [
            '[class*="player"]',
            '[class*="video"]',
            '[id*="player"]',
            '[id*="video"]',
            '[data-player]',
            '[data-video]'
        ];

        domAnalysis.potentialPlayers = [];
        playerSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                domAnalysis.potentialPlayers.push({
                    selector,
                    tagName: element.tagName,
                    className: element.className,
                    id: element.id
                });
            });
        });

        return domAnalysis;
    }

    /**
     * 检测加密和混淆技术 - 学术研究用
     */
    detectObfuscation() {
        const obfuscationAnalysis = {
            suspiciousScripts: [],
            encodedStrings: [],
            evalUsage: false,
            base64Strings: []
        };

        // 分析script标签
        const scripts = document.querySelectorAll('script');
        scripts.forEach((script, index) => {
            const content = script.textContent || script.innerHTML;
            if (content) {
                // 检测混淆特征
                const suspiciousPatterns = [
                    /eval\s*\(/gi,
                    /Function\s*\(/gi,
                    /atob\s*\(/gi,
                    /btoa\s*\(/gi,
                    /String\.fromCharCode/gi,
                    /\\x[0-9a-f]{2}/gi,
                    /\\u[0-9a-f]{4}/gi
                ];

                let suspiciousScore = 0;
                suspiciousPatterns.forEach(pattern => {
                    const matches = content.match(pattern);
                    if (matches) {
                        suspiciousScore += matches.length;
                    }
                });

                if (suspiciousScore > 5) {
                    obfuscationAnalysis.suspiciousScripts.push({
                        index,
                        score: suspiciousScore,
                        length: content.length,
                        src: script.src
                    });
                }

                // 检测Base64编码
                const base64Pattern = /[A-Za-z0-9+/]{20,}={0,2}/g;
                const base64Matches = content.match(base64Pattern);
                if (base64Matches) {
                    obfuscationAnalysis.base64Strings.push(...base64Matches.slice(0, 5)); // 只保存前5个
                }

                // 检测eval使用
                if (content.includes('eval(')) {
                    obfuscationAnalysis.evalUsage = true;
                }
            }
        });

        return obfuscationAnalysis;
    }

    /**
     * 收集完整的浏览器指纹 - 增强学术研究版
     */
    async collectFingerprint() {
        console.log('🔍 开始收集浏览器指纹信息...');

        try {
            this.fingerprint = {
                // 基础指纹信息
                timestamp: new Date().toISOString(),
                basic: this.collectBasicInfo(),
                screen: this.collectScreenInfo(),
                timezone: this.collectTimezoneInfo(),
                plugins: this.collectPluginsInfo(),
                fonts: await this.collectFontsInfo(),
                canvas: this.generateCanvasFingerprint(),
                webgl: this.generateWebGLFingerprint(),
                audio: await this.generateAudioFingerprint(),
                storage: this.collectStorageInfo(),

                // 学术研究增强功能
                networkRequests: this.networkRequests,
                apiEndpoints: this.apiEndpoints,
                videoSources: this.videoSources,
                videoElements: this.collectVideoElements(),
                domStructure: this.analyzeDOMStructure(),
                obfuscationAnalysis: this.detectObfuscation(),

                // 页面信息
                pageInfo: {
                    url: window.location.href,
                    title: document.title,
                    referrer: document.referrer,
                    domain: window.location.hostname,
                    protocol: window.location.protocol,
                    userAgent: navigator.userAgent
                }
            };

            // 生成唯一指纹哈希
            this.fingerprint.hash = this.generateFingerprintHash(this.fingerprint);

            console.log('✅ 指纹收集完成');
            console.log('📊 网络请求数量:', this.networkRequests.length);
            console.log('🎥 视频API数量:', this.apiEndpoints.length);
            console.log('📹 视频源数量:', this.videoSources.length);

            return this.fingerprint;

        } catch (error) {
            console.error('❌ 指纹收集失败:', error);
            return null;
        }
    }

    /**
     * 显示指纹信息
     */
    displayFingerprint() {
        if (!this.fingerprint) {
            console.log('❌ 没有可显示的指纹信息');
            return;
        }

        console.log('🖥️ 浏览器指纹信息:');
        console.log('==========================================');
        console.log('🆔 指纹哈希:', this.fingerprint.hash);
        console.log('📅 收集时间:', this.fingerprint.timestamp);
        console.log('🌐 用户代理:', this.fingerprint.basic.userAgent);
        console.log('🖥️ 平台:', this.fingerprint.basic.platform);
        console.log('📺 屏幕分辨率:', `${this.fingerprint.screen.screenWidth}x${this.fingerprint.screen.screenHeight}`);
        console.log('🌍 时区:', this.fingerprint.timezone.timezone);
        console.log('🔤 可用字体数量:', this.fingerprint.fonts.length);
        console.log('🎨 Canvas指纹:', this.fingerprint.canvas.substring(0, 50) + '...');
        console.log('🎮 WebGL信息:', this.fingerprint.webgl.renderer || 'N/A');
        console.log('🔊 音频指纹:', this.fingerprint.audio);
        console.log('==========================================');
    }

    /**
     * 导出指纹数据
     */
    exportFingerprint() {
        if (!this.fingerprint) {
            console.log('❌ 没有可导出的指纹信息');
            return;
        }

        const dataStr = JSON.stringify(this.fingerprint, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `browser_fingerprint_${this.fingerprint.hash}.json`;
        link.textContent = '📥 下载指纹数据';
        link.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            background: #007bff;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;

        document.body.appendChild(link);

        setTimeout(() => {
            link.click();
            console.log('📁 指纹数据已导出');
        }, 1000);
    }
}

// 使用示例
async function demonstrateFingerprinting() {
    console.log('🎓 浏览器指纹技术演示 - 仅用于学习目的');
    console.log('================================================');

    const collector = new BrowserFingerprintCollector();

    // 收集指纹
    const fingerprint = await collector.collectFingerprint();

    if (fingerprint) {
        // 显示指纹信息
        collector.displayFingerprint();

        // 导出数据
        collector.exportFingerprint();

        // 存储到全局变量供进一步分析
        window.browserFingerprint = fingerprint;
        console.log('💾 指纹数据已保存到 window.browserFingerprint');
    }
}

// 自动执行演示
demonstrateFingerprinting();

console.log(`
📚 浏览器指纹技术学习说明：

🎯 用途：
- 了解浏览器如何被唯一识别
- 学习Web隐私和安全技术
- 研究反指纹技术

⚠️ 注意事项：
- 此代码仅用于教育和研究目的
- 不应用于恶意跟踪或隐私侵犯
- 请遵守相关法律法规和道德准则

🔧 技术要点：
- Canvas指纹：利用图形渲染差异
- WebGL指纹：GPU和驱动信息
- 音频指纹：音频处理特征
- 字体检测：系统安装的字体
- 硬件信息：屏幕、CPU等特征

🛡️ 防护措施：
- 使用隐私浏览模式
- 安装反指纹扩展
- 定期清理浏览器数据
- 使用Tor浏览器等隐私工具
`);
